etcd:
  endpoints: etcd:2379
  rootPath: by-dev
  metaSubPath: meta
  kvSubPath: kv

minio:
  address: minio
  port: 9000
  accessKeyID: minioadmin
  secretAccessKey: minioadmin
  useSSL: false
  bucketName: milvus-bucket

messageQueue: rocksmq

rocksmq:
  path: /var/lib/milvus/rdb_data
  lrucacheratio: 0.12
  rocksmqPageSize: 134217728
  retentionTimeInMinutes: 2160
  retentionSizeInMB: 4096

localStorage:
  path: /var/lib/milvus/data

proxy:
  port: 19530
  maxTaskNum: 2048

rootCoord:
  port: 53100
  minSegmentSizeToEnableIndex: 512
  grpc:
    serverMaxSendSize: 1073741824
    serverMaxRecvSize: 536870912
    clientMaxSendSize: 536870912
    clientMaxRecvSize: 1073741824

dataCoord:
  port: 13333
  segment:
    maxSize: 2048
    diskSegmentMaxSize: 4096
    maxIdleTime: 300
    minSizeFromIdleToSealed: 32

queryCoord:
  port: 19531
  overloadedMemoryThresholdPercentage: 85
  memoryUsageMaxDifferencePercentage: 20
  balanceIntervalSeconds: 30

indexCoord:
  port: 31000

dataNode:
  port: 21124

queryNode:
  port: 21123
  segcore:
    cgoPoolSizeRatio: 4.0
    chunkRows: 256
    exprEvalBatchSize: 16384
    interimIndex:
      enableIndex: true
      nlist: 256
      nprobe: 32
      memExpansionRate: 1.25
      buildParallelRate: 0.75
  loadMemoryUsageFactor: 0.8
  cache:
    enabled: true
    memoryLimit: 4294967296
    warmup: async
    readAheadPolicy: willneed
  grouping:
    enabled: true
    maxNQ: 2000
    topKMergeRatio: 10

indexNode:
  port: 21121

quotaAndLimits:
  enabled: true
  limitWriting:
    memProtection:
      enabled: true
      dataNodeMemoryLowWaterLevel: 0.75
      dataNodeMemoryHighWaterLevel: 0.90
      queryNodeMemoryLowWaterLevel: 0.75
      queryNodeMemoryHighWaterLevel: 0.90