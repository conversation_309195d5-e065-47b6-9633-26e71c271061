# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

KEEP IN MIND, MOST IMPORTANT!!!!

Always reply in Chinese.

IMPORTANT!!!!!

Code-Context provide three mcp tools include index_codebase, search_code, clear_index. When start a new conversation and try to find code, DO NOT use 
index_codebase firstly. Just try to use search_code instead of tools like grep/find/pattern because search_code provide full context of code and can give more information of project.

## Zen Gemini
Study the code properly, think deeply about what this does and then see if there's any room for improvement in
terms of performance optimizations, brainstorm with gemini on this to get feedback and then confirm any change by
first adding a unit test with `measure` and measuring current code and then implementing the optimization and
measuring again to ensure it improved, then share results. Check with gemini in between as you make tweaks. Remember, do not be hesitate to ask gemini whether code worked as expected after you created/updated code.

AND REMEMBER:
Send source file code via zen chat mode by giving full of path file in `files` parameter whenever you need. Zen is always know much more correct things than you do.


## Project Overview

Code Context is a multi-platform semantic code search tool that integrates with AI coding assistants via Model Context Protocol (MCP). It consists of three main packages in a TypeScript monorepo:

- **@zilliz/code-context-core**: Core indexing engine with embedding and vector database integration
- **VSCode Extension**: Semantic Code Search extension for Visual Studio Code  
- **@zilliz/code-context-mcp**: Model Context Protocol server for AI agent integration

## Development Commands

### Package Manager
This project uses **pnpm** with workspaces. Node.js >=20.0.0 and pnpm >=10.0.0 are required.

### Build Commands
```bash
# Build all packages
pnpm build

# Build specific packages
pnpm build:core
pnpm build:vscode  
pnpm build:mcp

# Development with file watching
pnpm dev
pnpm dev:core
pnpm dev:vscode
pnpm dev:mcp
```

### Code Quality
```bash
# Lint all packages
pnpm lint
pnpm lint:fix

# Type checking
pnpm typecheck

# Clean build artifacts
pnpm clean
```

### Testing
The project uses Jest for testing. Run tests from individual package directories or use:
```bash
cd packages/core
npm test
```

### Release Commands
```bash
# Core package
pnpm release:core

# MCP server
pnpm release:mcp

# VSCode extension
pnpm release:vscode
```

## Architecture & Key Components

### Core Components (packages/core/src/)

1. **Code Splitters** (`splitter/`)
   - `AstCodeSplitter`: Uses tree-sitter for AST-based code parsing (preferred)
   - `LangChainCodeSplitter`: Character-based fallback splitter
   - Supports: TypeScript, JavaScript, Python, Java, C++, Go, Rust

2. **Embedding Providers** (`embedding/`)
   - `OpenAIEmbedding`: OpenAI embedding API
   - `VoyageAIEmbedding`: VoyageAI embedding API
   - `GeminiEmbedding`: Google Gemini embedding API
   - `OllamaEmbedding`: Local Ollama embedding

3. **Vector Databases** (`vectordb/`)
   - `MilvusVectorDatabase`: Milvus/Zilliz Cloud integration (gRPC)
   - `MilvusRestfulVectorDatabase`: RESTful API variant

4. **File Synchronization** (`sync/`)
   - `FileSynchronizer`: Merkle DAG-based incremental sync
   - `MerkleDAG`: File change detection using SHA256 hashes

### Key Configuration

- **Default chunk size**: 2500 characters with 300 character overlap
- **Supported extensions**: `.ts`, `.tsx`, `.js`, `.jsx`, `.py`, `.java`, `.cpp`, `.c`, `.h`, `.hpp`, `.cs`, `.go`, `.rs`, `.php`, `.rb`, `.swift`, `.kt`, `.scala`, `.m`, `.mm`, `.md`
- **Ignored patterns**: `node_modules/**`, `dist/**`, `build/**`, `.git/**`, `.vscode/**`, `.idea/**`
- **Embedding batch size**: 100 (configurable via `EMBEDDING_BATCH_SIZE`)
- **Chunk limit**: 450,000 per indexing operation

## Performance Considerations

### Known Performance Bottlenecks
1. **Sequential file processing**: Files are processed one by one during indexing
2. **Full filesystem scans**: Synchronizer scans entire directory tree on each sync
3. **Per-chunk hashing**: SHA256 hash computed for every code chunk
4. **Flat Merkle DAG**: Current implementation doesn't leverage hierarchical change detection

### Optimization Opportunities
1. **Parallelize file processing**: Use `p-limit` or similar for concurrent file handling
2. **Use filesystem watchers**: Replace polling with event-driven sync using `chokidar`
3. **Optimize chunk ID generation**: Use file hash + metadata instead of content hashing
4. **Implement proper Merkle tree**: Directory-based hierarchical hashing
5. **Cache regex patterns**: Cache compiled ignore pattern regexes
6. **Optimize vector DB operations**: Use filter-based deletion instead of query+delete

## Development Guidelines

### Code Style
- Uses ESLint with TypeScript recommended rules
- Unused variables allowed with `_` prefix pattern
- `no-explicit-any` as warning, not error

### Adding New Languages
To add Tree-sitter support for a new language:
1. Add language parser to dependencies in `packages/core/package.json`
2. Import parser in `ast-splitter.ts`
3. Define splittable node types in `SPLITTABLE_NODE_TYPES`
4. Add file extension mapping in `getLanguageFromExtension()`

### Vector Database Operations
- Milvus supports deletion by filter expression since v2.3.2:
  ```typescript
  await client.delete({
    collection_name: "collection_name",
    filter: "relativePath == \"path/to/file.ts\""
  });
  ```
- Use filter-based operations when possible to reduce network calls

### Environment Variables
- `OPENAI_API_KEY`: OpenAI API key for embeddings
- `MILVUS_ADDRESS`: Milvus server endpoint
- `MILVUS_TOKEN`: Milvus authentication token
- `EMBEDDING_BATCH_SIZE`: Batch size for embedding operations (default: 100)

## Testing Strategy

### Core Package Testing
- Focus on splitter accuracy with different languages
- Test embedding provider integrations
- Validate vector database operations
- Test file synchronization scenarios

### Performance Testing
- Measure indexing time with different chunk sizes
- Test concurrent file processing improvements
- Benchmark different embedding providers
- Validate memory usage during large codebase processing

## Troubleshooting

### Common Issues
1. **Tree-sitter parsing failures**: AST splitter falls back to LangChain splitter
2. **Collection limit reached**: Free Zilliz Cloud has collection limits
3. **Memory issues**: Large codebases may require chunk size optimization
4. **Token limits**: Embedding models have different token limits (OpenAI: 8192)

### MCP Integration
- MCP server redirects console output to stderr to avoid protocol interference
- Uses stdio transport for communication
- Supports both indexing and search operations via MCP tools

## Dependencies

### Core Dependencies
- `@google/genai`: Google Gemini integration
- `@zilliz/milvus2-sdk-node`: Milvus vector database
- `tree-sitter`: AST parsing
- `langchain`: Fallback text splitting
- `openai`, `voyageai`, `ollama`: Embedding providers

### Development Tools
- TypeScript 5.8.3
- ESLint with TypeScript support
- pnpm workspaces
- Webpack for VSCode extension bundling