{"name": "code-context-basic-example", "version": "0.0.7", "description": "Basic usage example for Code Context", "main": "index.ts", "scripts": {"start": "tsx index.ts", "build": "tsc index.ts --outDir dist --target es2020 --module commonjs --moduleResolution node --esModuleInterop true", "dev": "tsx --watch index.ts"}, "dependencies": {"@zilliz/code-context-core": "workspace:*"}, "devDependencies": {"tsx": "^4.0.0", "typescript": "^5.0.0", "@types/node": "^20.0.0", "dotenv": "^16.0.0"}, "private": true}