2025/07/29-06:32:05.211082 22 RocksDB version: 6.29.5
2025/07/29-06:32:05.212421 22 Git sha 0
2025/07/29-06:32:05.212425 22 Compile date 2024-04-29 01:22:15
2025/07/29-06:32:05.212433 22 DB SUMMARY
2025/07/29-06:32:05.212434 22 DB Session ID:  6B8Q0ZX9Y49GO2YQI6KA
2025/07/29-06:32:05.214560 22 CURRENT file:  CURRENT
2025/07/29-06:32:05.214563 22 IDENTITY file:  IDENTITY
2025/07/29-06:32:05.214847 22 MANIFEST file:  MANIFEST-000004 size: 59 Bytes
2025/07/29-06:32:05.214849 22 SST files in /var/lib/milvus/rdb_data_meta_kv dir, Total Num: 0, files: 
2025/07/29-06:32:05.214852 22 Write Ahead Log file in /var/lib/milvus/rdb_data_meta_kv: 000005.log size: 36 ; 
2025/07/29-06:32:05.214854 22                         Options.error_if_exists: 0
2025/07/29-06:32:05.214855 22                       Options.create_if_missing: 1
2025/07/29-06:32:05.214856 22                         Options.paranoid_checks: 1
2025/07/29-06:32:05.214857 22             Options.flush_verify_memtable_count: 1
2025/07/29-06:32:05.214858 22                               Options.track_and_verify_wals_in_manifest: 0
2025/07/29-06:32:05.214858 22                                     Options.env: 0xffff907453f0
2025/07/29-06:32:05.214859 22                                      Options.fs: PosixFileSystem
2025/07/29-06:32:05.214860 22                                Options.info_log: 0xffff44310050
2025/07/29-06:32:05.214861 22                Options.max_file_opening_threads: 16
2025/07/29-06:32:05.214862 22                              Options.statistics: (nil)
2025/07/29-06:32:05.214863 22                               Options.use_fsync: 0
2025/07/29-06:32:05.214864 22                       Options.max_log_file_size: 0
2025/07/29-06:32:05.214864 22                  Options.max_manifest_file_size: 1073741824
2025/07/29-06:32:05.214865 22                   Options.log_file_time_to_roll: 0
2025/07/29-06:32:05.214866 22                       Options.keep_log_file_num: 1000
2025/07/29-06:32:05.214867 22                    Options.recycle_log_file_num: 0
2025/07/29-06:32:05.214867 22                         Options.allow_fallocate: 1
2025/07/29-06:32:05.214868 22                        Options.allow_mmap_reads: 0
2025/07/29-06:32:05.214869 22                       Options.allow_mmap_writes: 0
2025/07/29-06:32:05.214870 22                        Options.use_direct_reads: 0
2025/07/29-06:32:05.214870 22                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/29-06:32:05.214871 22          Options.create_missing_column_families: 0
2025/07/29-06:32:05.214872 22                              Options.db_log_dir: 
2025/07/29-06:32:05.214872 22                                 Options.wal_dir: 
2025/07/29-06:32:05.214873 22                Options.table_cache_numshardbits: 6
2025/07/29-06:32:05.214874 22                         Options.WAL_ttl_seconds: 0
2025/07/29-06:32:05.214875 22                       Options.WAL_size_limit_MB: 0
2025/07/29-06:32:05.214875 22                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/29-06:32:05.214876 22             Options.manifest_preallocation_size: 4194304
2025/07/29-06:32:05.214877 22                     Options.is_fd_close_on_exec: 1
2025/07/29-06:32:05.214878 22                   Options.advise_random_on_open: 1
2025/07/29-06:32:05.214878 22                   Options.experimental_mempurge_threshold: 0.000000
2025/07/29-06:32:05.214880 22                    Options.db_write_buffer_size: 0
2025/07/29-06:32:05.214881 22                    Options.write_buffer_manager: 0xffff4be60180
2025/07/29-06:32:05.214882 22         Options.access_hint_on_compaction_start: 1
2025/07/29-06:32:05.214883 22  Options.new_table_reader_for_compaction_inputs: 0
2025/07/29-06:32:05.214883 22           Options.random_access_max_buffer_size: 1048576
2025/07/29-06:32:05.214884 22                      Options.use_adaptive_mutex: 0
2025/07/29-06:32:05.214885 22                            Options.rate_limiter: (nil)
2025/07/29-06:32:05.214887 22     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/29-06:32:05.214888 22                       Options.wal_recovery_mode: 2
2025/07/29-06:32:05.215080 22                  Options.enable_thread_tracking: 0
2025/07/29-06:32:05.215081 22                  Options.enable_pipelined_write: 0
2025/07/29-06:32:05.215082 22                  Options.unordered_write: 0
2025/07/29-06:32:05.215083 22         Options.allow_concurrent_memtable_write: 1
2025/07/29-06:32:05.215084 22      Options.enable_write_thread_adaptive_yield: 1
2025/07/29-06:32:05.215084 22             Options.write_thread_max_yield_usec: 100
2025/07/29-06:32:05.215085 22            Options.write_thread_slow_yield_usec: 3
2025/07/29-06:32:05.215086 22                               Options.row_cache: None
2025/07/29-06:32:05.215086 22                              Options.wal_filter: None
2025/07/29-06:32:05.215087 22             Options.avoid_flush_during_recovery: 0
2025/07/29-06:32:05.215088 22             Options.allow_ingest_behind: 0
2025/07/29-06:32:05.215089 22             Options.preserve_deletes: 0
2025/07/29-06:32:05.215090 22             Options.two_write_queues: 0
2025/07/29-06:32:05.215090 22             Options.manual_wal_flush: 0
2025/07/29-06:32:05.215091 22             Options.atomic_flush: 0
2025/07/29-06:32:05.215092 22             Options.avoid_unnecessary_blocking_io: 0
2025/07/29-06:32:05.215092 22                 Options.persist_stats_to_disk: 0
2025/07/29-06:32:05.215093 22                 Options.write_dbid_to_manifest: 0
2025/07/29-06:32:05.215094 22                 Options.log_readahead_size: 0
2025/07/29-06:32:05.215095 22                 Options.file_checksum_gen_factory: Unknown
2025/07/29-06:32:05.215095 22                 Options.best_efforts_recovery: 0
2025/07/29-06:32:05.215096 22                Options.max_bgerror_resume_count: 2147483647
2025/07/29-06:32:05.215097 22            Options.bgerror_resume_retry_interval: 1000000
2025/07/29-06:32:05.215098 22             Options.allow_data_in_errors: 0
2025/07/29-06:32:05.215098 22             Options.db_host_id: __hostname__
2025/07/29-06:32:05.215100 22             Options.max_background_jobs: 2
2025/07/29-06:32:05.215150 22             Options.max_background_compactions: -1
2025/07/29-06:32:05.215151 22             Options.max_subcompactions: 1
2025/07/29-06:32:05.215152 22             Options.avoid_flush_during_shutdown: 0
2025/07/29-06:32:05.215153 22           Options.writable_file_max_buffer_size: 1048576
2025/07/29-06:32:05.215153 22             Options.delayed_write_rate : 16777216
2025/07/29-06:32:05.215154 22             Options.max_total_wal_size: 0
2025/07/29-06:32:05.215155 22             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/29-06:32:05.215156 22                   Options.stats_dump_period_sec: 600
2025/07/29-06:32:05.215156 22                 Options.stats_persist_period_sec: 600
2025/07/29-06:32:05.215157 22                 Options.stats_history_buffer_size: 1048576
2025/07/29-06:32:05.215158 22                          Options.max_open_files: -1
2025/07/29-06:32:05.215159 22                          Options.bytes_per_sync: 0
2025/07/29-06:32:05.215159 22                      Options.wal_bytes_per_sync: 0
2025/07/29-06:32:05.215160 22                   Options.strict_bytes_per_sync: 0
2025/07/29-06:32:05.215161 22       Options.compaction_readahead_size: 0
2025/07/29-06:32:05.215162 22                  Options.max_background_flushes: 1
2025/07/29-06:32:05.215162 22 Compression algorithms supported:
2025/07/29-06:32:05.215163 22 	kZSTDNotFinalCompression supported: 1
2025/07/29-06:32:05.215165 22 	kZSTD supported: 1
2025/07/29-06:32:05.215166 22 	kXpressCompression supported: 0
2025/07/29-06:32:05.215166 22 	kLZ4HCCompression supported: 0
2025/07/29-06:32:05.215167 22 	kLZ4Compression supported: 0
2025/07/29-06:32:05.215168 22 	kBZip2Compression supported: 0
2025/07/29-06:32:05.215168 22 	kZlibCompression supported: 0
2025/07/29-06:32:05.215169 22 	kSnappyCompression supported: 0
2025/07/29-06:32:05.215172 22 Fast CRC32 supported: Not supported on Arm64
2025/07/29-06:32:05.216316 22 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data_meta_kv/MANIFEST-000004
2025/07/29-06:32:05.217371 22 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/07/29-06:32:05.217373 22               Options.comparator: leveldb.BytewiseComparator
2025/07/29-06:32:05.217374 22           Options.merge_operator: None
2025/07/29-06:32:05.217375 22        Options.compaction_filter: None
2025/07/29-06:32:05.217375 22        Options.compaction_filter_factory: None
2025/07/29-06:32:05.217376 22  Options.sst_partitioner_factory: None
2025/07/29-06:32:05.217377 22         Options.memtable_factory: SkipListFactory
2025/07/29-06:32:05.217378 22            Options.table_factory: BlockBasedTable
2025/07/29-06:32:05.217411 22            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff4be000e0)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0xffff4bea0010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/29-06:32:05.217413 22        Options.write_buffer_size: 67108864
2025/07/29-06:32:05.217414 22  Options.max_write_buffer_number: 2
2025/07/29-06:32:05.217415 22        Options.compression[0]: NoCompression
2025/07/29-06:32:05.217416 22        Options.compression[1]: NoCompression
2025/07/29-06:32:05.217417 22        Options.compression[2]: ZSTD
2025/07/29-06:32:05.217418 22        Options.compression[3]: ZSTD
2025/07/29-06:32:05.217419 22        Options.compression[4]: ZSTD
2025/07/29-06:32:05.217419 22                  Options.bottommost_compression: Disabled
2025/07/29-06:32:05.217420 22       Options.prefix_extractor: nullptr
2025/07/29-06:32:05.217421 22   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/29-06:32:05.217421 22             Options.num_levels: 5
2025/07/29-06:32:05.217422 22        Options.min_write_buffer_number_to_merge: 1
2025/07/29-06:32:05.217423 22     Options.max_write_buffer_number_to_maintain: 0
2025/07/29-06:32:05.217424 22     Options.max_write_buffer_size_to_maintain: 0
2025/07/29-06:32:05.217424 22            Options.bottommost_compression_opts.window_bits: -14
2025/07/29-06:32:05.217425 22                  Options.bottommost_compression_opts.level: 32767
2025/07/29-06:32:05.217426 22               Options.bottommost_compression_opts.strategy: 0
2025/07/29-06:32:05.217426 22         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/29-06:32:05.217427 22         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/29-06:32:05.217428 22         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/29-06:32:05.217429 22                  Options.bottommost_compression_opts.enabled: false
2025/07/29-06:32:05.217429 22         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/29-06:32:05.217430 22            Options.compression_opts.window_bits: -14
2025/07/29-06:32:05.217431 22                  Options.compression_opts.level: 32767
2025/07/29-06:32:05.217432 22               Options.compression_opts.strategy: 0
2025/07/29-06:32:05.217432 22         Options.compression_opts.max_dict_bytes: 0
2025/07/29-06:32:05.217433 22         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/29-06:32:05.217434 22         Options.compression_opts.parallel_threads: 1
2025/07/29-06:32:05.217715 22                  Options.compression_opts.enabled: false
2025/07/29-06:32:05.217717 22         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/29-06:32:05.217717 22      Options.level0_file_num_compaction_trigger: 4
2025/07/29-06:32:05.217718 22          Options.level0_slowdown_writes_trigger: 20
2025/07/29-06:32:05.217719 22              Options.level0_stop_writes_trigger: 36
2025/07/29-06:32:05.217719 22                   Options.target_file_size_base: 67108864
2025/07/29-06:32:05.217720 22             Options.target_file_size_multiplier: 2
2025/07/29-06:32:05.217721 22                Options.max_bytes_for_level_base: 268435456
2025/07/29-06:32:05.217722 22 Options.level_compaction_dynamic_level_bytes: 0
2025/07/29-06:32:05.217722 22          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/29-06:32:05.217724 22 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/29-06:32:05.217725 22 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/29-06:32:05.217725 22 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/29-06:32:05.217726 22 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/29-06:32:05.217727 22 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/29-06:32:05.217728 22 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/29-06:32:05.217728 22 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/29-06:32:05.217729 22       Options.max_sequential_skip_in_iterations: 8
2025/07/29-06:32:05.217730 22                    Options.max_compaction_bytes: 1677721600
2025/07/29-06:32:05.217730 22                        Options.arena_block_size: 1048576
2025/07/29-06:32:05.217731 22   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/29-06:32:05.217732 22   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/29-06:32:05.217732 22       Options.rate_limit_delay_max_milliseconds: 100
2025/07/29-06:32:05.217733 22                Options.disable_auto_compactions: 0
2025/07/29-06:32:05.217734 22                        Options.compaction_style: kCompactionStyleLevel
2025/07/29-06:32:05.217735 22                          Options.compaction_pri: kMinOverlappingRatio
2025/07/29-06:32:05.217736 22 Options.compaction_options_universal.size_ratio: 1
2025/07/29-06:32:05.217737 22 Options.compaction_options_universal.min_merge_width: 2
2025/07/29-06:32:05.217738 22 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/29-06:32:05.217738 22 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/29-06:32:05.217739 22 Options.compaction_options_universal.compression_size_percent: -1
2025/07/29-06:32:05.217740 22 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/29-06:32:05.217741 22 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/29-06:32:05.217741 22 Options.compaction_options_fifo.allow_compaction: 0
2025/07/29-06:32:05.217746 22                   Options.table_properties_collectors: 
2025/07/29-06:32:05.217746 22                   Options.inplace_update_support: 0
2025/07/29-06:32:05.217747 22                 Options.inplace_update_num_locks: 10000
2025/07/29-06:32:05.217748 22               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/29-06:32:05.217749 22               Options.memtable_whole_key_filtering: 0
2025/07/29-06:32:05.217749 22   Options.memtable_huge_page_size: 0
2025/07/29-06:32:05.217750 22                           Options.bloom_locality: 0
2025/07/29-06:32:05.217751 22                    Options.max_successive_merges: 0
2025/07/29-06:32:05.217751 22                Options.optimize_filters_for_hits: 0
2025/07/29-06:32:05.217752 22                Options.paranoid_file_checks: 0
2025/07/29-06:32:05.217753 22                Options.force_consistency_checks: 1
2025/07/29-06:32:05.217753 22                Options.report_bg_io_stats: 0
2025/07/29-06:32:05.217754 22                               Options.ttl: 2592000
2025/07/29-06:32:05.217755 22          Options.periodic_compaction_seconds: 0
2025/07/29-06:32:05.217928 22                       Options.enable_blob_files: false
2025/07/29-06:32:05.217929 22                           Options.min_blob_size: 0
2025/07/29-06:32:05.217930 22                          Options.blob_file_size: 268435456
2025/07/29-06:32:05.217931 22                   Options.blob_compression_type: NoCompression
2025/07/29-06:32:05.217932 22          Options.enable_blob_garbage_collection: false
2025/07/29-06:32:05.217932 22      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/29-06:32:05.217933 22 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/29-06:32:05.217934 22          Options.blob_compaction_readahead_size: 0
2025/07/29-06:32:05.231691 22 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data_meta_kv/MANIFEST-000004 succeeded,manifest_file_number is 4, next_file_number is 6, last_sequence is 0, log_number is 0,prev_log_number is 0,max_column_family is 0,min_log_number_to_keep is 0
2025/07/29-06:32:05.231698 22 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2025/07/29-06:32:05.232624 22 [db/version_set.cc:4409] Creating manifest 8
2025/07/29-06:32:05.237613 22 EVENT_LOG_v1 {"time_micros": 1753770725237603, "job": 1, "event": "recovery_started", "wal_files": [5]}
2025/07/29-06:32:05.237619 22 [db/db_impl/db_impl_open.cc:888] Recovering log #5 mode 2
2025/07/29-06:32:05.240303 22 EVENT_LOG_v1 {"time_micros": 1753770725240282, "cf_name": "default", "job": 1, "event": "table_file_creation", "file_number": 9, "file_size": 975, "file_checksum": "", "file_checksum_func_name": "Unknown", "table_properties": {"data_size": 38, "index_size": 23, "index_partitions": 0, "top_level_index_size": 0, "index_key_is_user_key": 1, "index_value_is_delta_encoded": 1, "filter_size": 0, "raw_key_size": 14, "raw_average_key_size": 14, "raw_value_size": 8, "raw_average_value_size": 8, "num_data_blocks": 1, "num_entries": 1, "num_filter_entries": 0, "num_deletions": 0, "num_merge_operands": 0, "num_range_deletions": 0, "format_version": 0, "fixed_key_len": 0, "filter_policy": "", "column_family_name": "default", "column_family_id": 0, "comparator": "leveldb.BytewiseComparator", "merge_operator": "nullptr", "prefix_extractor_name": "nullptr", "property_collectors": "[]", "compression": "NoCompression", "compression_options": "window_bits=-14; level=32767; strategy=0; max_dict_bytes=0; zstd_max_train_bytes=0; enabled=0; max_dict_buffer_bytes=0; ", "creation_time": 1753770725, "oldest_key_time": 0, "file_creation_time": 0, "slow_compression_estimated_data_size": 0, "fast_compression_estimated_data_size": 0, "db_id": "c3964cbc-6ae2-4617-9cde-9d774c2d90fb", "db_session_id": "6B8Q0ZX9Y49GO2YQI6KA", "orig_file_number": 9}}
2025/07/29-06:32:05.240566 22 [db/version_set.cc:4409] Creating manifest 10
2025/07/29-06:32:05.247862 22 EVENT_LOG_v1 {"time_micros": 1753770725247856, "job": 1, "event": "recovery_finished"}
2025/07/29-06:32:05.253589 22 [file/delete_scheduler.cc:73] Deleted file /var/lib/milvus/rdb_data_meta_kv/000005.log immediately, rate_bytes_per_sec 0, total_trash_size 0 max_trash_db_ratio 0.250000
2025/07/29-06:32:05.253863 22 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0xffff44410000
2025/07/29-06:32:05.254418 22 DB pointer 0xffff442a0000
2025/07/29-06:32:05.255567 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-06:32:05.255592 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 0.0 total, 0.0 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 0.0 total, 0.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.02 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.02 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 1 last_copies: 0 last_secs: 0.000273 secs_since: 0
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-06:42:05.261563 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-06:42:05.261786 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 600.0 total, 600.0 interval
Cumulative writes: 11K writes, 11K keys, 8451 commit groups, 1.3 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 11K writes, 0 syncs, 11131.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 11K writes, 11K keys, 8451 commit groups, 1.3 writes per commit group, ingest: 0.41 MB, 0.00 MB/s
Interval WAL: 11K writes, 0 syncs, 11131.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 600.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 2 last_copies: 0 last_secs: 8.4e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-06:52:05.263955 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-06:52:05.264425 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1200.0 total, 600.0 interval
Cumulative writes: 23K writes, 23K keys, 17K commit groups, 1.3 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 23K writes, 0 syncs, 23131.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 12K writes, 12K keys, 8894 commit groups, 1.3 writes per commit group, ingest: 0.45 MB, 0.00 MB/s
Interval WAL: 12K writes, 0 syncs, 12000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1200.0 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 3 last_copies: 0 last_secs: 9.6e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-07:02:05.269112 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-07:02:05.269510 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1800.1 total, 600.0 interval
Cumulative writes: 37K writes, 37K keys, 26K commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 37K writes, 0 syncs, 37595.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 14K writes, 14K keys, 9124 commit groups, 1.6 writes per commit group, ingest: 0.53 MB, 0.00 MB/s
Interval WAL: 14K writes, 0 syncs, 14464.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1800.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 4 last_copies: 0 last_secs: 9.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-07:12:05.272378 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-07:12:05.272972 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2400.1 total, 600.0 interval
Cumulative writes: 50K writes, 50K keys, 35K commit groups, 1.4 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 50K writes, 0 syncs, 50463.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 12K writes, 12K keys, 8855 commit groups, 1.5 writes per commit group, ingest: 0.48 MB, 0.00 MB/s
Interval WAL: 12K writes, 0 syncs, 12868.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2400.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 5 last_copies: 0 last_secs: 0.000149 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-07:22:05.274471 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-07:22:05.274735 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3000.1 total, 600.0 interval
Cumulative writes: 65K writes, 65K keys, 44K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 65K writes, 0 syncs, 65463.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 9262 commit groups, 1.6 writes per commit group, ingest: 0.55 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3000.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 6 last_copies: 0 last_secs: 4.1e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-07:32:05.276011 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-07:32:05.276190 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3600.1 total, 600.0 interval
Cumulative writes: 80K writes, 80K keys, 53K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 80K writes, 0 syncs, 80463.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 9205 commit groups, 1.6 writes per commit group, ingest: 0.55 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3600.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 7 last_copies: 0 last_secs: 5.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-07:42:05.277836 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-07:42:05.278242 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4200.1 total, 600.0 interval
Cumulative writes: 95K writes, 95K keys, 62K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 95K writes, 0 syncs, 95241.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 14K writes, 14K keys, 9165 commit groups, 1.6 writes per commit group, ingest: 0.54 MB, 0.00 MB/s
Interval WAL: 14K writes, 0 syncs, 14778.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4200.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 8 last_copies: 0 last_secs: 7.8e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-07:52:05.279891 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-07:52:05.282105 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4800.1 total, 600.0 interval
Cumulative writes: 110K writes, 110K keys, 72K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 110K writes, 0 syncs, 110266.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 9247 commit groups, 1.6 writes per commit group, ingest: 0.56 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15025.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4800.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 9 last_copies: 0 last_secs: 8.7e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-08:02:05.284516 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-08:02:05.284982 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5400.1 total, 600.0 interval
Cumulative writes: 125K writes, 125K keys, 81K commit groups, 1.5 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 125K writes, 0 syncs, 125272.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 9244 commit groups, 1.6 writes per commit group, ingest: 0.56 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15006.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5400.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 10 last_copies: 0 last_secs: 4.3e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-08:12:05.286958 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-08:12:05.288016 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6000.1 total, 600.0 interval
Cumulative writes: 140K writes, 140K keys, 90K commit groups, 1.5 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 140K writes, 0 syncs, 140277.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 9127 commit groups, 1.6 writes per commit group, ingest: 0.55 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15005.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6000.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 11 last_copies: 0 last_secs: 3e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-08:22:05.289571 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-08:22:05.290691 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6600.1 total, 600.0 interval
Cumulative writes: 155K writes, 155K keys, 99K commit groups, 1.6 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 155K writes, 0 syncs, 155277.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 9126 commit groups, 1.6 writes per commit group, ingest: 0.55 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6600.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 12 last_copies: 0 last_secs: 3.9e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-08:32:05.290920 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-08:32:05.291462 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7200.1 total, 600.0 interval
Cumulative writes: 170K writes, 170K keys, 108K commit groups, 1.6 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 170K writes, 0 syncs, 170277.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 9105 commit groups, 1.6 writes per commit group, ingest: 0.55 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7200.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 13 last_copies: 0 last_secs: 4.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-08:42:05.292880 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-08:42:05.294322 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7800.1 total, 600.0 interval
Cumulative writes: 185K writes, 185K keys, 117K commit groups, 1.6 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 185K writes, 0 syncs, 185277.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 9113 commit groups, 1.6 writes per commit group, ingest: 0.55 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7800.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 14 last_copies: 0 last_secs: 9.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-08:52:05.295857 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-08:52:05.295886 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 8400.1 total, 600.0 interval
Cumulative writes: 200K writes, 200K keys, 127K commit groups, 1.6 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 200K writes, 0 syncs, 200277.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 9179 commit groups, 1.6 writes per commit group, ingest: 0.55 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8400.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 15 last_copies: 0 last_secs: 7.5e-05 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
2025/07/29-09:02:05.301352 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-09:02:05.301389 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9000.1 total, 600.0 interval
Cumulative writes: 215K writes, 215K keys, 136K commit groups, 1.6 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 215K writes, 0 syncs, 215277.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 9208 commit groups, 1.6 writes per commit group, ingest: 0.55 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
  L0      1/0    0.95 KB   0.2      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Sum      1/0    0.95 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   1.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
User      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.4      0.00              0.00         1    0.002       0      0       0.0       0.0

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9000.1 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 16 last_copies: 0 last_secs: 0.000157 secs_since: 0
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **
