2025/07/29-06:32:05.256659 22 RocksDB version: 6.29.5
2025/07/29-06:32:05.256799 22 Git sha 0
2025/07/29-06:32:05.256801 22 Compile date 2024-04-29 01:22:15
2025/07/29-06:32:05.256802 22 DB SUMMARY
2025/07/29-06:32:05.256803 22 DB Session ID:  6B8Q0ZX9Y49GO2YQI6KB
2025/07/29-06:32:05.257110 22 CURRENT file:  CURRENT
2025/07/29-06:32:05.257111 22 IDENTITY file:  IDENTITY
2025/07/29-06:32:05.257201 22 MANIFEST file:  MANIFEST-000004 size: 116 Bytes
2025/07/29-06:32:05.257202 22 SST files in /var/lib/milvus/rdb_data dir, Total Num: 0, files: 
2025/07/29-06:32:05.257203 22 Write Ahead Log file in /var/lib/milvus/rdb_data: 000005.log size: 0 ; 
2025/07/29-06:32:05.257204 22                         Options.error_if_exists: 0
2025/07/29-06:32:05.257205 22                       Options.create_if_missing: 1
2025/07/29-06:32:05.257206 22                         Options.paranoid_checks: 1
2025/07/29-06:32:05.257206 22             Options.flush_verify_memtable_count: 1
2025/07/29-06:32:05.257207 22                               Options.track_and_verify_wals_in_manifest: 0
2025/07/29-06:32:05.257208 22                                     Options.env: 0xffff907453f0
2025/07/29-06:32:05.257209 22                                      Options.fs: PosixFileSystem
2025/07/29-06:32:05.257210 22                                Options.info_log: 0xffff44310140
2025/07/29-06:32:05.257210 22                Options.max_file_opening_threads: 16
2025/07/29-06:32:05.257211 22                              Options.statistics: (nil)
2025/07/29-06:32:05.257212 22                               Options.use_fsync: 0
2025/07/29-06:32:05.257213 22                       Options.max_log_file_size: 0
2025/07/29-06:32:05.257214 22                  Options.max_manifest_file_size: 1073741824
2025/07/29-06:32:05.257214 22                   Options.log_file_time_to_roll: 0
2025/07/29-06:32:05.257215 22                       Options.keep_log_file_num: 1000
2025/07/29-06:32:05.257216 22                    Options.recycle_log_file_num: 0
2025/07/29-06:32:05.257216 22                         Options.allow_fallocate: 1
2025/07/29-06:32:05.257217 22                        Options.allow_mmap_reads: 0
2025/07/29-06:32:05.257218 22                       Options.allow_mmap_writes: 0
2025/07/29-06:32:05.257219 22                        Options.use_direct_reads: 0
2025/07/29-06:32:05.257219 22                        Options.use_direct_io_for_flush_and_compaction: 0
2025/07/29-06:32:05.257220 22          Options.create_missing_column_families: 1
2025/07/29-06:32:05.257221 22                              Options.db_log_dir: 
2025/07/29-06:32:05.257222 22                                 Options.wal_dir: 
2025/07/29-06:32:05.257222 22                Options.table_cache_numshardbits: 6
2025/07/29-06:32:05.257223 22                         Options.WAL_ttl_seconds: 0
2025/07/29-06:32:05.257224 22                       Options.WAL_size_limit_MB: 0
2025/07/29-06:32:05.257225 22                        Options.max_write_batch_group_size_bytes: 1048576
2025/07/29-06:32:05.257225 22             Options.manifest_preallocation_size: 4194304
2025/07/29-06:32:05.257226 22                     Options.is_fd_close_on_exec: 1
2025/07/29-06:32:05.257227 22                   Options.advise_random_on_open: 1
2025/07/29-06:32:05.257228 22                   Options.experimental_mempurge_threshold: 0.000000
2025/07/29-06:32:05.257229 22                    Options.db_write_buffer_size: 0
2025/07/29-06:32:05.257230 22                    Options.write_buffer_manager: 0xffff4be60480
2025/07/29-06:32:05.257230 22         Options.access_hint_on_compaction_start: 1
2025/07/29-06:32:05.257231 22  Options.new_table_reader_for_compaction_inputs: 0
2025/07/29-06:32:05.257232 22           Options.random_access_max_buffer_size: 1048576
2025/07/29-06:32:05.257233 22                      Options.use_adaptive_mutex: 0
2025/07/29-06:32:05.257233 22                            Options.rate_limiter: (nil)
2025/07/29-06:32:05.257234 22     Options.sst_file_manager.rate_bytes_per_sec: 0
2025/07/29-06:32:05.257235 22                       Options.wal_recovery_mode: 2
2025/07/29-06:32:05.257236 22                  Options.enable_thread_tracking: 0
2025/07/29-06:32:05.257369 22                  Options.enable_pipelined_write: 0
2025/07/29-06:32:05.257370 22                  Options.unordered_write: 0
2025/07/29-06:32:05.257370 22         Options.allow_concurrent_memtable_write: 1
2025/07/29-06:32:05.257371 22      Options.enable_write_thread_adaptive_yield: 1
2025/07/29-06:32:05.257372 22             Options.write_thread_max_yield_usec: 100
2025/07/29-06:32:05.257373 22            Options.write_thread_slow_yield_usec: 3
2025/07/29-06:32:05.257373 22                               Options.row_cache: None
2025/07/29-06:32:05.257374 22                              Options.wal_filter: None
2025/07/29-06:32:05.257375 22             Options.avoid_flush_during_recovery: 0
2025/07/29-06:32:05.257376 22             Options.allow_ingest_behind: 0
2025/07/29-06:32:05.257376 22             Options.preserve_deletes: 0
2025/07/29-06:32:05.257377 22             Options.two_write_queues: 0
2025/07/29-06:32:05.257378 22             Options.manual_wal_flush: 0
2025/07/29-06:32:05.257379 22             Options.atomic_flush: 0
2025/07/29-06:32:05.257379 22             Options.avoid_unnecessary_blocking_io: 0
2025/07/29-06:32:05.257380 22                 Options.persist_stats_to_disk: 0
2025/07/29-06:32:05.257381 22                 Options.write_dbid_to_manifest: 0
2025/07/29-06:32:05.257382 22                 Options.log_readahead_size: 0
2025/07/29-06:32:05.257383 22                 Options.file_checksum_gen_factory: Unknown
2025/07/29-06:32:05.257383 22                 Options.best_efforts_recovery: 0
2025/07/29-06:32:05.257384 22                Options.max_bgerror_resume_count: 2147483647
2025/07/29-06:32:05.257385 22            Options.bgerror_resume_retry_interval: 1000000
2025/07/29-06:32:05.257386 22             Options.allow_data_in_errors: 0
2025/07/29-06:32:05.257386 22             Options.db_host_id: __hostname__
2025/07/29-06:32:05.257387 22             Options.max_background_jobs: 2
2025/07/29-06:32:05.257388 22             Options.max_background_compactions: -1
2025/07/29-06:32:05.257389 22             Options.max_subcompactions: 1
2025/07/29-06:32:05.257389 22             Options.avoid_flush_during_shutdown: 0
2025/07/29-06:32:05.257390 22           Options.writable_file_max_buffer_size: 1048576
2025/07/29-06:32:05.257391 22             Options.delayed_write_rate : 16777216
2025/07/29-06:32:05.257392 22             Options.max_total_wal_size: 0
2025/07/29-06:32:05.257392 22             Options.delete_obsolete_files_period_micros: 21600000000
2025/07/29-06:32:05.257393 22                   Options.stats_dump_period_sec: 600
2025/07/29-06:32:05.257394 22                 Options.stats_persist_period_sec: 600
2025/07/29-06:32:05.257395 22                 Options.stats_history_buffer_size: 1048576
2025/07/29-06:32:05.257396 22                          Options.max_open_files: -1
2025/07/29-06:32:05.257396 22                          Options.bytes_per_sync: 0
2025/07/29-06:32:05.257397 22                      Options.wal_bytes_per_sync: 0
2025/07/29-06:32:05.257398 22                   Options.strict_bytes_per_sync: 0
2025/07/29-06:32:05.257399 22       Options.compaction_readahead_size: 0
2025/07/29-06:32:05.257399 22                  Options.max_background_flushes: 1
2025/07/29-06:32:05.257400 22 Compression algorithms supported:
2025/07/29-06:32:05.257401 22 	kZSTDNotFinalCompression supported: 1
2025/07/29-06:32:05.257402 22 	kZSTD supported: 1
2025/07/29-06:32:05.257403 22 	kXpressCompression supported: 0
2025/07/29-06:32:05.257404 22 	kLZ4HCCompression supported: 0
2025/07/29-06:32:05.257404 22 	kLZ4Compression supported: 0
2025/07/29-06:32:05.257405 22 	kBZip2Compression supported: 0
2025/07/29-06:32:05.257406 22 	kZlibCompression supported: 0
2025/07/29-06:32:05.257407 22 	kSnappyCompression supported: 0
2025/07/29-06:32:05.257408 22 Fast CRC32 supported: Not supported on Arm64
2025/07/29-06:32:05.259639 22 [db/version_set.cc:4888] Recovering from manifest file: /var/lib/milvus/rdb_data/MANIFEST-000004
2025/07/29-06:32:05.261159 22 [db/column_family.cc:605] --------------- Options for column family [default]:
2025/07/29-06:32:05.261165 22               Options.comparator: leveldb.BytewiseComparator
2025/07/29-06:32:05.261166 22           Options.merge_operator: None
2025/07/29-06:32:05.261167 22        Options.compaction_filter: None
2025/07/29-06:32:05.261167 22        Options.compaction_filter_factory: None
2025/07/29-06:32:05.261168 22  Options.sst_partitioner_factory: None
2025/07/29-06:32:05.261169 22         Options.memtable_factory: SkipListFactory
2025/07/29-06:32:05.261170 22            Options.table_factory: BlockBasedTable
2025/07/29-06:32:05.261182 22            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff4be01300)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0xffff4bea0010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/29-06:32:05.261184 22        Options.write_buffer_size: 67108864
2025/07/29-06:32:05.261184 22  Options.max_write_buffer_number: 2
2025/07/29-06:32:05.261185 22        Options.compression[0]: NoCompression
2025/07/29-06:32:05.261186 22        Options.compression[1]: NoCompression
2025/07/29-06:32:05.261187 22        Options.compression[2]: ZSTD
2025/07/29-06:32:05.261188 22        Options.compression[3]: ZSTD
2025/07/29-06:32:05.261188 22        Options.compression[4]: ZSTD
2025/07/29-06:32:05.261189 22                  Options.bottommost_compression: Disabled
2025/07/29-06:32:05.261190 22       Options.prefix_extractor: nullptr
2025/07/29-06:32:05.261191 22   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/29-06:32:05.261191 22             Options.num_levels: 5
2025/07/29-06:32:05.261192 22        Options.min_write_buffer_number_to_merge: 1
2025/07/29-06:32:05.261193 22     Options.max_write_buffer_number_to_maintain: 0
2025/07/29-06:32:05.261194 22     Options.max_write_buffer_size_to_maintain: 0
2025/07/29-06:32:05.261194 22            Options.bottommost_compression_opts.window_bits: -14
2025/07/29-06:32:05.261195 22                  Options.bottommost_compression_opts.level: 32767
2025/07/29-06:32:05.261196 22               Options.bottommost_compression_opts.strategy: 0
2025/07/29-06:32:05.261197 22         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/29-06:32:05.261198 22         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/29-06:32:05.261198 22         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/29-06:32:05.261199 22                  Options.bottommost_compression_opts.enabled: false
2025/07/29-06:32:05.261200 22         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/29-06:32:05.261201 22            Options.compression_opts.window_bits: -14
2025/07/29-06:32:05.261201 22                  Options.compression_opts.level: 32767
2025/07/29-06:32:05.261202 22               Options.compression_opts.strategy: 0
2025/07/29-06:32:05.261203 22         Options.compression_opts.max_dict_bytes: 0
2025/07/29-06:32:05.261203 22         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/29-06:32:05.261204 22         Options.compression_opts.parallel_threads: 1
2025/07/29-06:32:05.261592 22                  Options.compression_opts.enabled: false
2025/07/29-06:32:05.261593 22         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/29-06:32:05.261594 22      Options.level0_file_num_compaction_trigger: 4
2025/07/29-06:32:05.261595 22          Options.level0_slowdown_writes_trigger: 20
2025/07/29-06:32:05.261595 22              Options.level0_stop_writes_trigger: 36
2025/07/29-06:32:05.261596 22                   Options.target_file_size_base: 67108864
2025/07/29-06:32:05.261597 22             Options.target_file_size_multiplier: 2
2025/07/29-06:32:05.261597 22                Options.max_bytes_for_level_base: 268435456
2025/07/29-06:32:05.261598 22 Options.level_compaction_dynamic_level_bytes: 0
2025/07/29-06:32:05.261599 22          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/29-06:32:05.261601 22 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/29-06:32:05.261601 22 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/29-06:32:05.261602 22 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/29-06:32:05.261603 22 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/29-06:32:05.261604 22 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/29-06:32:05.261604 22 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/29-06:32:05.261605 22 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/29-06:32:05.261606 22       Options.max_sequential_skip_in_iterations: 8
2025/07/29-06:32:05.261606 22                    Options.max_compaction_bytes: 1677721600
2025/07/29-06:32:05.261607 22                        Options.arena_block_size: 1048576
2025/07/29-06:32:05.261608 22   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/29-06:32:05.261608 22   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/29-06:32:05.261609 22       Options.rate_limit_delay_max_milliseconds: 100
2025/07/29-06:32:05.261610 22                Options.disable_auto_compactions: 0
2025/07/29-06:32:05.261611 22                        Options.compaction_style: kCompactionStyleLevel
2025/07/29-06:32:05.261613 22                          Options.compaction_pri: kMinOverlappingRatio
2025/07/29-06:32:05.261613 22 Options.compaction_options_universal.size_ratio: 1
2025/07/29-06:32:05.261614 22 Options.compaction_options_universal.min_merge_width: 2
2025/07/29-06:32:05.261615 22 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/29-06:32:05.261615 22 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/29-06:32:05.261616 22 Options.compaction_options_universal.compression_size_percent: -1
2025/07/29-06:32:05.261617 22 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/29-06:32:05.261618 22 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/29-06:32:05.261619 22 Options.compaction_options_fifo.allow_compaction: 0
2025/07/29-06:32:05.261623 22                   Options.table_properties_collectors: 
2025/07/29-06:32:05.261623 22                   Options.inplace_update_support: 0
2025/07/29-06:32:05.261624 22                 Options.inplace_update_num_locks: 10000
2025/07/29-06:32:05.261625 22               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/29-06:32:05.261626 22               Options.memtable_whole_key_filtering: 0
2025/07/29-06:32:05.261626 22   Options.memtable_huge_page_size: 0
2025/07/29-06:32:05.261627 22                           Options.bloom_locality: 0
2025/07/29-06:32:05.261628 22                    Options.max_successive_merges: 0
2025/07/29-06:32:05.261628 22                Options.optimize_filters_for_hits: 0
2025/07/29-06:32:05.261629 22                Options.paranoid_file_checks: 0
2025/07/29-06:32:05.261630 22                Options.force_consistency_checks: 1
2025/07/29-06:32:05.261630 22                Options.report_bg_io_stats: 0
2025/07/29-06:32:05.261631 22                               Options.ttl: 2592000
2025/07/29-06:32:05.261632 22          Options.periodic_compaction_seconds: 0
2025/07/29-06:32:05.261752 22                       Options.enable_blob_files: false
2025/07/29-06:32:05.261753 22                           Options.min_blob_size: 0
2025/07/29-06:32:05.261754 22                          Options.blob_file_size: 268435456
2025/07/29-06:32:05.261755 22                   Options.blob_compression_type: NoCompression
2025/07/29-06:32:05.261755 22          Options.enable_blob_garbage_collection: false
2025/07/29-06:32:05.261756 22      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/29-06:32:05.261757 22 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/29-06:32:05.261758 22          Options.blob_compaction_readahead_size: 0
2025/07/29-06:32:05.262062 22 [db/column_family.cc:605] --------------- Options for column family [properties]:
2025/07/29-06:32:05.262064 22               Options.comparator: leveldb.BytewiseComparator
2025/07/29-06:32:05.262064 22           Options.merge_operator: None
2025/07/29-06:32:05.262065 22        Options.compaction_filter: None
2025/07/29-06:32:05.262066 22        Options.compaction_filter_factory: None
2025/07/29-06:32:05.262066 22  Options.sst_partitioner_factory: None
2025/07/29-06:32:05.262067 22         Options.memtable_factory: SkipListFactory
2025/07/29-06:32:05.262068 22            Options.table_factory: BlockBasedTable
2025/07/29-06:32:05.262075 22            table_factory options:   flush_block_policy_factory: FlushBlockBySizePolicyFactory (0xffff4be01300)
  cache_index_and_filter_blocks: 0
  cache_index_and_filter_blocks_with_high_priority: 1
  pin_l0_filter_and_index_blocks_in_cache: 0
  pin_top_level_index_and_filter: 1
  index_type: 0
  data_block_index_type: 0
  index_shortening: 1
  data_block_hash_table_util_ratio: 0.750000
  hash_index_allow_collision: 1
  checksum: 1
  no_block_cache: 0
  block_cache: 0xffff4bea0010
  block_cache_name: LRUCache
  block_cache_options:
    capacity : 4294967296
    num_shard_bits : 6
    strict_capacity_limit : 0
    memory_allocator : None
    high_pri_pool_ratio: 0.500
  block_cache_compressed: (nil)
  persistent_cache: (nil)
  block_size: 65536
  block_size_deviation: 10
  block_restart_interval: 16
  index_block_restart_interval: 1
  metadata_block_size: 4096
  partition_filters: 0
  use_delta_encoding: 1
  filter_policy: nullptr
  whole_key_filtering: 1
  verify_compression: 0
  read_amp_bytes_per_bit: 0
  format_version: 5
  enable_index_compression: 1
  block_align: 0
  max_auto_readahead_size: 262144
  prepopulate_block_cache: 0
2025/07/29-06:32:05.262076 22        Options.write_buffer_size: 67108864
2025/07/29-06:32:05.262077 22  Options.max_write_buffer_number: 2
2025/07/29-06:32:05.262078 22        Options.compression[0]: NoCompression
2025/07/29-06:32:05.262079 22        Options.compression[1]: NoCompression
2025/07/29-06:32:05.262079 22        Options.compression[2]: ZSTD
2025/07/29-06:32:05.262080 22        Options.compression[3]: ZSTD
2025/07/29-06:32:05.262081 22        Options.compression[4]: ZSTD
2025/07/29-06:32:05.262081 22                  Options.bottommost_compression: Disabled
2025/07/29-06:32:05.262082 22       Options.prefix_extractor: nullptr
2025/07/29-06:32:05.262083 22   Options.memtable_insert_with_hint_prefix_extractor: nullptr
2025/07/29-06:32:05.262083 22             Options.num_levels: 5
2025/07/29-06:32:05.262084 22        Options.min_write_buffer_number_to_merge: 1
2025/07/29-06:32:05.262085 22     Options.max_write_buffer_number_to_maintain: 0
2025/07/29-06:32:05.262086 22     Options.max_write_buffer_size_to_maintain: 0
2025/07/29-06:32:05.262086 22            Options.bottommost_compression_opts.window_bits: -14
2025/07/29-06:32:05.262087 22                  Options.bottommost_compression_opts.level: 32767
2025/07/29-06:32:05.262088 22               Options.bottommost_compression_opts.strategy: 0
2025/07/29-06:32:05.262088 22         Options.bottommost_compression_opts.max_dict_bytes: 0
2025/07/29-06:32:05.262089 22         Options.bottommost_compression_opts.zstd_max_train_bytes: 0
2025/07/29-06:32:05.262090 22         Options.bottommost_compression_opts.parallel_threads: 1
2025/07/29-06:32:05.262217 22                  Options.bottommost_compression_opts.enabled: false
2025/07/29-06:32:05.262218 22         Options.bottommost_compression_opts.max_dict_buffer_bytes: 0
2025/07/29-06:32:05.262219 22            Options.compression_opts.window_bits: -14
2025/07/29-06:32:05.262220 22                  Options.compression_opts.level: 32767
2025/07/29-06:32:05.262221 22               Options.compression_opts.strategy: 0
2025/07/29-06:32:05.262221 22         Options.compression_opts.max_dict_bytes: 0
2025/07/29-06:32:05.262222 22         Options.compression_opts.zstd_max_train_bytes: 0
2025/07/29-06:32:05.262223 22         Options.compression_opts.parallel_threads: 1
2025/07/29-06:32:05.262223 22                  Options.compression_opts.enabled: false
2025/07/29-06:32:05.262224 22         Options.compression_opts.max_dict_buffer_bytes: 0
2025/07/29-06:32:05.262225 22      Options.level0_file_num_compaction_trigger: 4
2025/07/29-06:32:05.262225 22          Options.level0_slowdown_writes_trigger: 20
2025/07/29-06:32:05.262226 22              Options.level0_stop_writes_trigger: 36
2025/07/29-06:32:05.262227 22                   Options.target_file_size_base: 67108864
2025/07/29-06:32:05.262228 22             Options.target_file_size_multiplier: 2
2025/07/29-06:32:05.262228 22                Options.max_bytes_for_level_base: 268435456
2025/07/29-06:32:05.262229 22 Options.level_compaction_dynamic_level_bytes: 0
2025/07/29-06:32:05.262230 22          Options.max_bytes_for_level_multiplier: 10.000000
2025/07/29-06:32:05.262231 22 Options.max_bytes_for_level_multiplier_addtl[0]: 1
2025/07/29-06:32:05.262232 22 Options.max_bytes_for_level_multiplier_addtl[1]: 1
2025/07/29-06:32:05.262232 22 Options.max_bytes_for_level_multiplier_addtl[2]: 1
2025/07/29-06:32:05.262233 22 Options.max_bytes_for_level_multiplier_addtl[3]: 1
2025/07/29-06:32:05.262234 22 Options.max_bytes_for_level_multiplier_addtl[4]: 1
2025/07/29-06:32:05.262234 22 Options.max_bytes_for_level_multiplier_addtl[5]: 1
2025/07/29-06:32:05.262235 22 Options.max_bytes_for_level_multiplier_addtl[6]: 1
2025/07/29-06:32:05.262236 22       Options.max_sequential_skip_in_iterations: 8
2025/07/29-06:32:05.262236 22                    Options.max_compaction_bytes: 1677721600
2025/07/29-06:32:05.262237 22                        Options.arena_block_size: 1048576
2025/07/29-06:32:05.262238 22   Options.soft_pending_compaction_bytes_limit: 68719476736
2025/07/29-06:32:05.262239 22   Options.hard_pending_compaction_bytes_limit: 274877906944
2025/07/29-06:32:05.262239 22       Options.rate_limit_delay_max_milliseconds: 100
2025/07/29-06:32:05.262240 22                Options.disable_auto_compactions: 0
2025/07/29-06:32:05.262241 22                        Options.compaction_style: kCompactionStyleLevel
2025/07/29-06:32:05.262242 22                          Options.compaction_pri: kMinOverlappingRatio
2025/07/29-06:32:05.262242 22 Options.compaction_options_universal.size_ratio: 1
2025/07/29-06:32:05.262243 22 Options.compaction_options_universal.min_merge_width: 2
2025/07/29-06:32:05.262244 22 Options.compaction_options_universal.max_merge_width: 4294967295
2025/07/29-06:32:05.262244 22 Options.compaction_options_universal.max_size_amplification_percent: 200
2025/07/29-06:32:05.262245 22 Options.compaction_options_universal.compression_size_percent: -1
2025/07/29-06:32:05.262246 22 Options.compaction_options_universal.stop_style: kCompactionStopStyleTotalSize
2025/07/29-06:32:05.262246 22 Options.compaction_options_fifo.max_table_files_size: 1073741824
2025/07/29-06:32:05.262247 22 Options.compaction_options_fifo.allow_compaction: 0
2025/07/29-06:32:05.262249 22                   Options.table_properties_collectors: 
2025/07/29-06:32:05.262249 22                   Options.inplace_update_support: 0
2025/07/29-06:32:05.262250 22                 Options.inplace_update_num_locks: 10000
2025/07/29-06:32:05.262251 22               Options.memtable_prefix_bloom_size_ratio: 0.000000
2025/07/29-06:32:05.262252 22               Options.memtable_whole_key_filtering: 0
2025/07/29-06:32:05.263172 22   Options.memtable_huge_page_size: 0
2025/07/29-06:32:05.263173 22                           Options.bloom_locality: 0
2025/07/29-06:32:05.263174 22                    Options.max_successive_merges: 0
2025/07/29-06:32:05.263174 22                Options.optimize_filters_for_hits: 0
2025/07/29-06:32:05.263175 22                Options.paranoid_file_checks: 0
2025/07/29-06:32:05.263176 22                Options.force_consistency_checks: 1
2025/07/29-06:32:05.263176 22                Options.report_bg_io_stats: 0
2025/07/29-06:32:05.263177 22                               Options.ttl: 2592000
2025/07/29-06:32:05.263178 22          Options.periodic_compaction_seconds: 0
2025/07/29-06:32:05.263178 22                       Options.enable_blob_files: false
2025/07/29-06:32:05.263179 22                           Options.min_blob_size: 0
2025/07/29-06:32:05.263180 22                          Options.blob_file_size: 268435456
2025/07/29-06:32:05.263181 22                   Options.blob_compression_type: NoCompression
2025/07/29-06:32:05.263181 22          Options.enable_blob_garbage_collection: false
2025/07/29-06:32:05.263182 22      Options.blob_garbage_collection_age_cutoff: 0.250000
2025/07/29-06:32:05.263183 22 Options.blob_garbage_collection_force_threshold: 1.000000
2025/07/29-06:32:05.263184 22          Options.blob_compaction_readahead_size: 0
2025/07/29-06:32:05.286708 22 [db/version_set.cc:4928] Recovered from manifest file:/var/lib/milvus/rdb_data/MANIFEST-000004 succeeded,manifest_file_number is 4, next_file_number is 7, last_sequence is 0, log_number is 5,prev_log_number is 0,max_column_family is 1,min_log_number_to_keep is 0
2025/07/29-06:32:05.286717 22 [db/version_set.cc:4943] Column family [default] (ID 0), log number is 0
2025/07/29-06:32:05.286718 22 [db/version_set.cc:4943] Column family [properties] (ID 1), log number is 5
2025/07/29-06:32:05.292419 22 [db/version_set.cc:4409] Creating manifest 10
2025/07/29-06:32:05.368794 22 EVENT_LOG_v1 {"time_micros": 1753770725368780, "job": 1, "event": "recovery_started", "wal_files": [5]}
2025/07/29-06:32:05.368805 22 [db/db_impl/db_impl_open.cc:888] Recovering log #5 mode 2
2025/07/29-06:32:05.372335 22 [db/version_set.cc:4409] Creating manifest 11
2025/07/29-06:32:05.432025 22 EVENT_LOG_v1 {"time_micros": 1753770725432018, "job": 1, "event": "recovery_finished"}
2025/07/29-06:32:05.451362 22 [db/db_impl/db_impl_open.cc:1820] SstFileManager instance 0xffff44410700
2025/07/29-06:32:05.456488 22 DB pointer 0xffff442a1c00
2025/07/29-06:32:08.458484 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-06:32:08.458514 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3.2 total, 3.2 interval
Cumulative writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 0 writes, 0 keys, 0 commit groups, 0.0 writes per commit group, ingest: 0.00 MB, 0.00 MB/s
Interval WAL: 0 writes, 0 syncs, 0.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.2 total, 3.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 0.000273 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3.2 total, 3.2 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 1 last_copies: 2 last_secs: 0.000273 secs_since: 3
Block cache entry stats(count,size,portion): Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-06:42:08.468025 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-06:42:08.469379 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 603.2 total, 600.0 interval
Cumulative writes: 11K writes, 11K keys, 5990 commit groups, 1.9 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 11K writes, 0 syncs, 11177.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 11K writes, 11K keys, 5990 commit groups, 1.9 writes per commit group, ingest: 0.59 MB, 0.00 MB/s
Interval WAL: 11K writes, 0 syncs, 11177.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 603.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 2 last_copies: 2 last_secs: 8.4e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 603.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 2 last_copies: 2 last_secs: 8.4e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-06:52:08.474420 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-06:52:08.475280 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1203.2 total, 600.0 interval
Cumulative writes: 23K writes, 23K keys, 12K commit groups, 1.9 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 23K writes, 0 syncs, 23177.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 12K writes, 12K keys, 6186 commit groups, 1.9 writes per commit group, ingest: 0.62 MB, 0.00 MB/s
Interval WAL: 12K writes, 0 syncs, 12000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 3 last_copies: 2 last_secs: 9.6e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 3 last_copies: 2 last_secs: 9.6e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-07:02:08.484367 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-07:02:08.485029 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 1803.2 total, 600.0 interval
Cumulative writes: 37K writes, 37K keys, 18K commit groups, 2.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 37K writes, 0 syncs, 37641.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 14K writes, 14K keys, 6374 commit groups, 2.3 writes per commit group, ingest: 0.74 MB, 0.00 MB/s
Interval WAL: 14K writes, 0 syncs, 14464.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 4 last_copies: 2 last_secs: 9.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 1803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 4 last_copies: 2 last_secs: 9.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-07:12:08.486462 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-07:12:08.486657 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 2403.2 total, 600.0 interval
Cumulative writes: 50K writes, 50K keys, 24K commit groups, 2.0 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 50K writes, 0 syncs, 50525.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 12K writes, 12K keys, 6196 commit groups, 2.1 writes per commit group, ingest: 0.66 MB, 0.00 MB/s
Interval WAL: 12K writes, 0 syncs, 12884.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2403.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 5 last_copies: 2 last_secs: 0.000149 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 2403.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 5 last_copies: 2 last_secs: 0.000149 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-07:22:08.492550 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-07:22:08.493386 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3003.2 total, 600.0 interval
Cumulative writes: 65K writes, 65K keys, 31K commit groups, 2.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 65K writes, 0 syncs, 65525.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 6433 commit groups, 2.3 writes per commit group, ingest: 0.76 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3003.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 6 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3003.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 6 last_copies: 2 last_secs: 4.1e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-07:32:08.499365 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-07:32:08.499883 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 3603.2 total, 600.0 interval
Cumulative writes: 80K writes, 80K keys, 37K commit groups, 2.1 writes per commit group, ingest: 0.00 GB, 0.00 MB/s
Cumulative WAL: 80K writes, 0 syncs, 80525.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 6399 commit groups, 2.3 writes per commit group, ingest: 0.76 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3603.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 7 last_copies: 2 last_secs: 5.8e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 3603.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 7 last_copies: 2 last_secs: 5.8e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-07:42:08.501459 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-07:42:08.502178 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4203.2 total, 600.0 interval
Cumulative writes: 95K writes, 95K keys, 43K commit groups, 2.2 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 95K writes, 0 syncs, 95304.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 14K writes, 14K keys, 6371 commit groups, 2.3 writes per commit group, ingest: 1.07 MB, 0.00 MB/s
Interval WAL: 14K writes, 0 syncs, 14779.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 8 last_copies: 2 last_secs: 7.8e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4203.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 8 last_copies: 2 last_secs: 7.8e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-07:52:08.503710 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-07:52:08.504217 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 4803.2 total, 600.0 interval
Cumulative writes: 110K writes, 110K keys, 50K commit groups, 2.2 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 110K writes, 0 syncs, 110328.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 6427 commit groups, 2.3 writes per commit group, ingest: 4.51 MB, 0.01 MB/s
Interval WAL: 15K writes, 0 syncs, 15024.00 writes per sync, written: 0.00 GB, 0.01 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 9 last_copies: 2 last_secs: 8.7e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 4803.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 9 last_copies: 2 last_secs: 8.7e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-08:02:08.506823 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-08:02:08.507231 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 5403.2 total, 600.0 interval
Cumulative writes: 125K writes, 125K keys, 56K commit groups, 2.2 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 125K writes, 0 syncs, 125334.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 6477 commit groups, 2.3 writes per commit group, ingest: 1.68 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15006.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5403.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 10 last_copies: 2 last_secs: 4.3e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 5403.2 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 10 last_copies: 2 last_secs: 4.3e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-08:12:08.512160 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-08:12:08.513873 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6003.3 total, 600.0 interval
Cumulative writes: 140K writes, 140K keys, 63K commit groups, 2.2 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 140K writes, 0 syncs, 140339.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 6357 commit groups, 2.4 writes per commit group, ingest: 0.76 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15005.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6003.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 11 last_copies: 2 last_secs: 3e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6003.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 11 last_copies: 2 last_secs: 3e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-08:22:08.519224 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-08:22:08.519833 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 6603.3 total, 600.0 interval
Cumulative writes: 155K writes, 155K keys, 69K commit groups, 2.2 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 155K writes, 0 syncs, 155339.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 6362 commit groups, 2.4 writes per commit group, ingest: 0.76 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6603.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 12 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 6603.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 12 last_copies: 2 last_secs: 3.9e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-08:32:08.521094 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-08:32:08.521531 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7203.3 total, 600.0 interval
Cumulative writes: 170K writes, 170K keys, 75K commit groups, 2.2 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 170K writes, 0 syncs, 170339.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 6367 commit groups, 2.4 writes per commit group, ingest: 0.76 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7203.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 13 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7203.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 13 last_copies: 2 last_secs: 4.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-08:42:08.526813 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-08:42:08.527859 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 7803.3 total, 600.0 interval
Cumulative writes: 185K writes, 185K keys, 82K commit groups, 2.3 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 185K writes, 0 syncs, 185339.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 6356 commit groups, 2.4 writes per commit group, ingest: 0.76 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7803.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 14 last_copies: 2 last_secs: 9.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 7803.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 14 last_copies: 2 last_secs: 9.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-08:52:08.534888 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-08:52:08.535865 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 8403.3 total, 600.0 interval
Cumulative writes: 200K writes, 200K keys, 88K commit groups, 2.3 writes per commit group, ingest: 0.01 GB, 0.00 MB/s
Cumulative WAL: 200K writes, 0 syncs, 200339.00 writes per sync, written: 0.01 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 6374 commit groups, 2.4 writes per commit group, ingest: 0.76 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8403.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 15 last_copies: 2 last_secs: 7.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 8403.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 15 last_copies: 2 last_secs: 7.5e-05 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
2025/07/29-09:02:08.540202 51 [db/db_impl/db_impl.cc:1013] ------- DUMPING STATS -------
2025/07/29-09:02:08.540262 51 [db/db_impl/db_impl.cc:1015] 
** DB Stats **
Uptime(secs): 9003.3 total, 600.0 interval
Cumulative writes: 215K writes, 215K keys, 95K commit groups, 2.3 writes per commit group, ingest: 0.02 GB, 0.00 MB/s
Cumulative WAL: 215K writes, 0 syncs, 215339.00 writes per sync, written: 0.02 GB, 0.00 MB/s
Cumulative stall: 00:00:0.000 H:M:S, 0.0 percent
Interval writes: 15K writes, 15K keys, 6443 commit groups, 2.3 writes per commit group, ingest: 0.76 MB, 0.00 MB/s
Interval WAL: 15K writes, 0 syncs, 15000.00 writes per sync, written: 0.00 GB, 0.00 MB/s
Interval stall: 00:00:0.000 H:M:S, 0.0 percent

** Compaction Stats [default] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [default] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9003.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 16 last_copies: 2 last_secs: 0.000157 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** Compaction Stats [properties] **
Level    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------
 Sum      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0
 Int      0/0    0.00 KB   0.0      0.0     0.0      0.0       0.0      0.0       0.0   0.0      0.0      0.0      0.00              0.00         0    0.000       0      0       0.0       0.0

** Compaction Stats [properties] **
Priority    Files   Size     Score Read(GB)  Rn(GB) Rnp1(GB) Write(GB) Wnew(GB) Moved(GB) W-Amp Rd(MB/s) Wr(MB/s) Comp(sec) CompMergeCPU(sec) Comp(cnt) Avg(sec) KeyIn KeyDrop Rblob(GB) Wblob(GB)
---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------

Blob file count: 0, total size: 0.0 GB

Uptime(secs): 9003.3 total, 600.0 interval
Flush(GB): cumulative 0.000, interval 0.000
AddFile(GB): cumulative 0.000, interval 0.000
AddFile(Total Files): cumulative 0, interval 0
AddFile(L0 Files): cumulative 0, interval 0
AddFile(Keys): cumulative 0, interval 0
Cumulative compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Interval compaction: 0.00 GB write, 0.00 MB/s write, 0.00 GB read, 0.00 MB/s read, 0.0 seconds
Stalls(count): 0 level0_slowdown, 0 level0_slowdown_with_compaction, 0 level0_numfiles, 0 level0_numfiles_with_compaction, 0 stop for pending_compaction_bytes, 0 slowdown for pending_compaction_bytes, 0 memtable_compaction, 0 memtable_slowdown, interval 0 total count
Block cache LRUCache@0xffff4bea0010#8 capacity: 4.00 GB collections: 16 last_copies: 2 last_secs: 0.000157 secs_since: 3
Block cache entry stats(count,size,portion): DataBlock(1,0.12 KB,2.98023e-06%) Misc(1,0.00 KB,0%)

** File Read Latency Histogram By Level [default] **

** File Read Latency Histogram By Level [properties] **
