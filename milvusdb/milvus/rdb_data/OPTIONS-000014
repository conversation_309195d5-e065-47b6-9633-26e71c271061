# This is a RocksDB option file.
#
# For detailed file format spec, please refer to the example file
# in examples/rocksdb_option_file_example.ini
#

[Version]
  rocksdb_version=6.29.5
  options_file_version=1.1

[DBOptions]
  max_open_files=-1
  stats_history_buffer_size=1048576
  stats_persist_period_sec=600
  max_background_flushes=1
  stats_dump_period_sec=600
  compaction_readahead_size=0
  bytes_per_sync=0
  delete_obsolete_files_period_micros=21600000000
  max_total_wal_size=0
  delayed_write_rate=16777216
  wal_bytes_per_sync=0
  writable_file_max_buffer_size=1048576
  avoid_flush_during_shutdown=false
  max_subcompactions=1
  strict_bytes_per_sync=false
  max_background_compactions=-1
  base_background_compactions=-1
  max_background_jobs=2
  file_checksum_gen_factory=nullptr
  db_host_id=__hostname__
  bgerror_resume_retry_interval=1000000
  best_efforts_recovery=false
  avoid_unnecessary_blocking_io=false
  two_write_queues=false
  atomic_flush=false
  preserve_deletes=false
  allow_ingest_behind=false
  lowest_used_cache_tier=kNonVolatileBlockTier
  avoid_flush_during_recovery=false
  info_log_level=INFO_LEVEL
  access_hint_on_compaction_start=NORMAL
  max_bgerror_resume_count=2147483647
  write_thread_slow_yield_usec=3
  allow_concurrent_memtable_write=true
  WAL_ttl_seconds=0
  manual_wal_flush=false
  dump_malloc_stats=false
  wal_recovery_mode=kPointInTimeRecovery
  log_file_time_to_roll=0
  enable_write_thread_adaptive_yield=true
  recycle_log_file_num=0
  table_cache_numshardbits=6
  max_file_opening_threads=16
  allow_data_in_errors=false
  rate_limiter=nullptr
  use_fsync=false
  unordered_write=false
  fail_if_options_file_error=false
  random_access_max_buffer_size=1048576
  new_table_reader_for_compaction_inputs=false
  skip_checking_sst_file_sizes_on_db_open=false
  skip_stats_update_on_db_open=false
  persist_stats_to_disk=false
  track_and_verify_wals_in_manifest=false
  enable_pipelined_write=false
  flush_verify_memtable_count=true
  log_readahead_size=0
  is_fd_close_on_exec=true
  WAL_size_limit_MB=0
  experimental_mempurge_threshold=0.000000
  write_dbid_to_manifest=false
  use_adaptive_mutex=false
  error_if_exists=false
  write_thread_max_yield_usec=100
  enable_thread_tracking=false
  db_write_buffer_size=0
  create_missing_column_families=true
  paranoid_checks=true
  create_if_missing=true
  wal_filter=nullptr
  max_manifest_file_size=1073741824
  allow_2pc=false
  use_direct_io_for_flush_and_compaction=false
  manifest_preallocation_size=4194304
  use_direct_reads=false
  allow_fallocate=true
  max_write_batch_group_size_bytes=1048576
  keep_log_file_num=1000
  allow_mmap_reads=false
  max_log_file_size=0
  allow_mmap_writes=false
  advise_random_on_open=true
  

[CFOptions "default"]
  compression_opts={max_dict_buffer_bytes=0;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;strategy=0;max_dict_bytes=0;level=32767;window_bits=-14;}
  bottommost_compression=kDisableCompressionOption
  enable_blob_garbage_collection=false
  blob_file_size=268435456
  sample_for_compression=0
  periodic_compaction_seconds=0
  ttl=2592000
  blob_garbage_collection_age_cutoff=0.250000
  compaction_options_universal={allow_trivial_move=false;incremental=false;stop_style=kCompactionStopStyleTotalSize;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;min_merge_width=2;size_ratio=1;}
  compression=kNoCompression
  max_sequential_skip_in_iterations=8
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  max_bytes_for_level_multiplier=10.000000
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  max_successive_merges=0
  blob_compaction_readahead_size=0
  inplace_update_num_locks=10000
  arena_block_size=1048576
  target_file_size_multiplier=2
  prefix_extractor=nullptr
  max_write_buffer_number=2
  blob_compression_type=kNoCompression
  level0_stop_writes_trigger=36
  level0_slowdown_writes_trigger=20
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=1073741824;}
  level0_file_num_compaction_trigger=4
  write_buffer_size=67108864
  memtable_huge_page_size=0
  max_compaction_bytes=1677721600
  min_blob_size=0
  bottommost_compression_opts={max_dict_buffer_bytes=0;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;strategy=0;max_dict_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  target_file_size_base=67108864
  soft_pending_compaction_bytes_limit=68719476736
  enable_blob_files=false
  paranoid_file_checks=false
  check_flush_compaction_key_order=true
  blob_garbage_collection_force_threshold=1.000000
  disable_auto_compactions=false
  memtable_prefix_bloom_size_ratio=0.000000
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_filter_factory=nullptr
  comparator=leveldb.BytewiseComparator
  sst_partitioner_factory=nullptr
  bloom_locality=0
  compaction_style=kCompactionStyleLevel
  min_write_buffer_number_to_merge=1
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  merge_operator=nullptr
  memtable_factory=SkipListFactory
  memtable_insert_with_hint_prefix_extractor=nullptr
  compression_per_level=kNoCompression:kNoCompression:kZSTD:kZSTD:kZSTD
  num_levels=5
  force_consistency_checks=true
  optimize_filters_for_hits=false
  compaction_filter=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  table_factory=BlockBasedTable
  
[TableOptions/BlockBasedTable "default"]
  pin_top_level_index_and_filter=true
  block_align=false
  read_amp_bytes_per_bit=0
  verify_compression=false
  enable_index_compression=true
  reserve_table_builder_memory=false
  whole_key_filtering=true
  max_auto_readahead_size=262144
  optimize_filters_for_memory=false
  index_block_restart_interval=1
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  block_size=65536
  format_version=5
  partition_filters=false
  block_size_deviation=10
  no_block_cache=false
  checksum=kCRC32c
  data_block_hash_table_util_ratio=0.750000
  index_shortening=kShortenSeparators
  data_block_index_type=kDataBlockBinarySearch
  hash_index_allow_collision=true
  filter_policy=nullptr
  metadata_block_size=4096
  index_type=kBinarySearch
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  cache_index_and_filter_blocks=false
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  

[CFOptions "properties"]
  compression_opts={max_dict_buffer_bytes=0;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;strategy=0;max_dict_bytes=0;level=32767;window_bits=-14;}
  bottommost_compression=kDisableCompressionOption
  enable_blob_garbage_collection=false
  blob_file_size=268435456
  sample_for_compression=0
  periodic_compaction_seconds=0
  ttl=2592000
  blob_garbage_collection_age_cutoff=0.250000
  compaction_options_universal={allow_trivial_move=false;incremental=false;stop_style=kCompactionStopStyleTotalSize;compression_size_percent=-1;max_size_amplification_percent=200;max_merge_width=4294967295;min_merge_width=2;size_ratio=1;}
  compression=kNoCompression
  max_sequential_skip_in_iterations=8
  max_bytes_for_level_multiplier_additional=1:1:1:1:1:1:1
  max_bytes_for_level_multiplier=10.000000
  max_bytes_for_level_base=268435456
  memtable_whole_key_filtering=false
  max_successive_merges=0
  blob_compaction_readahead_size=0
  inplace_update_num_locks=10000
  arena_block_size=1048576
  target_file_size_multiplier=2
  prefix_extractor=nullptr
  max_write_buffer_number=2
  blob_compression_type=kNoCompression
  level0_stop_writes_trigger=36
  level0_slowdown_writes_trigger=20
  compaction_options_fifo={allow_compaction=false;age_for_warm=0;max_table_files_size=1073741824;}
  level0_file_num_compaction_trigger=4
  write_buffer_size=67108864
  memtable_huge_page_size=0
  max_compaction_bytes=1677721600
  min_blob_size=0
  bottommost_compression_opts={max_dict_buffer_bytes=0;enabled=false;parallel_threads=1;zstd_max_train_bytes=0;strategy=0;max_dict_bytes=0;level=32767;window_bits=-14;}
  hard_pending_compaction_bytes_limit=274877906944
  target_file_size_base=67108864
  soft_pending_compaction_bytes_limit=68719476736
  enable_blob_files=false
  paranoid_file_checks=false
  check_flush_compaction_key_order=true
  blob_garbage_collection_force_threshold=1.000000
  disable_auto_compactions=false
  memtable_prefix_bloom_size_ratio=0.000000
  report_bg_io_stats=false
  compaction_pri=kMinOverlappingRatio
  compaction_filter_factory=nullptr
  comparator=leveldb.BytewiseComparator
  sst_partitioner_factory=nullptr
  bloom_locality=0
  compaction_style=kCompactionStyleLevel
  min_write_buffer_number_to_merge=1
  max_write_buffer_size_to_maintain=0
  max_write_buffer_number_to_maintain=0
  merge_operator=nullptr
  memtable_factory=SkipListFactory
  memtable_insert_with_hint_prefix_extractor=nullptr
  compression_per_level=kNoCompression:kNoCompression:kZSTD:kZSTD:kZSTD
  num_levels=5
  force_consistency_checks=true
  optimize_filters_for_hits=false
  compaction_filter=nullptr
  level_compaction_dynamic_level_bytes=false
  inplace_update_support=false
  table_factory=BlockBasedTable
  
[TableOptions/BlockBasedTable "properties"]
  pin_top_level_index_and_filter=true
  block_align=false
  read_amp_bytes_per_bit=0
  verify_compression=false
  enable_index_compression=true
  reserve_table_builder_memory=false
  whole_key_filtering=true
  max_auto_readahead_size=262144
  optimize_filters_for_memory=false
  index_block_restart_interval=1
  prepopulate_block_cache=kDisable
  block_restart_interval=16
  block_size=65536
  format_version=5
  partition_filters=false
  block_size_deviation=10
  no_block_cache=false
  checksum=kCRC32c
  data_block_hash_table_util_ratio=0.750000
  index_shortening=kShortenSeparators
  data_block_index_type=kDataBlockBinarySearch
  hash_index_allow_collision=true
  filter_policy=nullptr
  metadata_block_size=4096
  index_type=kBinarySearch
  metadata_cache_options={unpartitioned_pinning=kFallback;partition_pinning=kFallback;top_level_index_pinning=kFallback;}
  pin_l0_filter_and_index_blocks_in_cache=false
  cache_index_and_filter_blocks_with_high_priority=true
  cache_index_and_filter_blocks=false
  flush_block_policy_factory=FlushBlockBySizePolicyFactory
  
