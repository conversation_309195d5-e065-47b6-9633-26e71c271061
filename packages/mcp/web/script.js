// 全局变量
let updateInterval = null;
let currentTasks = [];

// API基础路径
const API_BASE = '/api';

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('🚀 Code Context 索引管理器已启动');
    initializePage();
    startAutoRefresh();
});

// 初始化页面
function initializePage() {
    loadTasks();
    updateLastUpdateTime();
}

// 开始自动刷新
function startAutoRefresh() {
    // 每5秒刷新一次任务状态
    updateInterval = setInterval(() => {
        loadTasks();
        updateLastUpdateTime();
    }, 5000);
}

// 停止自动刷新
function stopAutoRefresh() {
    if (updateInterval) {
        clearInterval(updateInterval);
        updateInterval = null;
    }
}

// 更新最后更新时间
function updateLastUpdateTime() {
    const now = new Date();
    const timeString = now.toLocaleTimeString('zh-CN');
    const lastUpdateElement = document.getElementById('last-update');
    
    if (lastUpdateElement) {
        lastUpdateElement.textContent = timeString;
    }
}

// 加载任务列表
async function loadTasks() {
    const container = document.getElementById('tasks-container');
    const loading = document.getElementById('loading');
    const statusIndicator = document.getElementById('status-indicator');
    const serverStatus = document.getElementById('server-status');
    
    // 检查必要的DOM元素是否存在
    if (!container || !loading || !statusIndicator || !serverStatus) {
        console.error('关键DOM元素未找到:', {
            container: !!container,
            loading: !!loading,
            statusIndicator: !!statusIndicator,
            serverStatus: !!serverStatus
        });
        return;
    }
    
    try {
        // 显示加载状态
        if (currentTasks.length === 0) {
            loading.style.display = 'flex';
        }
        
        const response = await fetch(`${API_BASE}/tasks`);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const data = await response.json();
        
        if (!data.success) {
            throw new Error(data.error || '获取任务列表失败');
        }
        
        // 更新服务器状态
        statusIndicator.textContent = '🟢';
        serverStatus.textContent = '服务器连接正常';
        
        // 更新任务数据
        currentTasks = data.data.tasks;
        
        // 更新概览统计
        updateOverviewStats(data.data);
        
        // 渲染任务列表
        renderTasks(currentTasks);
        
    } catch (error) {
        console.error('加载任务失败:', error);
        
        // 更新服务器状态 - 添加null检查
        if (statusIndicator) {
            statusIndicator.textContent = '🔴';
        }
        if (serverStatus) {
            serverStatus.textContent = '服务器连接失败';
        }
        
        // 显示错误信息
        showError('加载任务失败: ' + error.message);
    } finally {
        // 隐藏加载状态 - 添加null检查
        if (loading) {
            loading.style.display = 'none';
        }
    }
}

// 更新概览统计
function updateOverviewStats(data) {
    const activeTasksElement = document.getElementById('active-tasks');
    const indexedCodebasesElement = document.getElementById('indexed-codebases');
    
    if (activeTasksElement) {
        activeTasksElement.textContent = data.totalTasks;
    }
    if (indexedCodebasesElement) {
        indexedCodebasesElement.textContent = data.indexedCodebases;
    }
}

// 渲染任务列表
function renderTasks(tasks) {
    const container = document.getElementById('tasks-container');
    
    if (!container) {
        console.error('tasks-container 元素未找到');
        return;
    }
    
    if (tasks.length === 0) {
        container.innerHTML = `
            <div class="empty-state">
                <div class="empty-state-icon">📋</div>
                <h3>暂无索引任务</h3>
                <p>当前没有正在进行或暂停的索引任务。<br>使用 MCP 工具开始新的索引任务。</p>
            </div>
        `;
        return;
    }
    
    const tasksHtml = tasks.map(task => renderTaskCard(task)).join('');
    container.innerHTML = tasksHtml;
}

// 渲染单个任务卡片
function renderTaskCard(task) {
    const statusClass = `status-${task.status}`;
    const statusText = getStatusText(task.status);
    const progressPercentage = parseFloat(task.progress.percentage) || 0;
    
    // 生成操作按钮
    let actionsHtml = '';
    if (task.status === 'indexing') {
        actionsHtml = `
            <button class="btn btn-danger" onclick="pauseTask('${task.taskId}')">
                ⏸️ 暂停
            </button>
        `;
    } else if (task.status === 'paused') {
        actionsHtml = `
            <button class="btn btn-primary" onclick="resumeTask('${task.taskId}')">
                ▶️ 继续
            </button>
        `;
    }
    
    // 错误信息
    const errorHtml = task.lastError ? `
        <div class="error-message">
            <strong>⚠️ 错误:</strong> ${escapeHtml(task.lastError)}
        </div>
    ` : '';
    
    return `
        <div class="task-card">
            <div class="task-header">
                <div class="task-info">
                    <h3>${escapeHtml(task.path)}</h3>
                    <div class="task-id">Task ID: ${task.taskId}</div>
                </div>
                <div class="task-actions">
                    <div class="status-badge ${statusClass}">${statusText}</div>
                    ${actionsHtml}
                </div>
            </div>
            
            <div class="progress-section">
                <div class="progress-info">
                    <span>进度: ${task.progress.processedFiles} / ${task.progress.totalFiles} 文件</span>
                    <span>${task.progress.percentage}</span>
                </div>
                <div class="progress-bar">
                    <div class="progress-fill" style="width: ${progressPercentage}%"></div>
                </div>
            </div>
            
            <div class="task-details">
                <div class="detail-row">
                    <span class="detail-label">生成代码块:</span>
                    <span class="detail-value">${task.totalChunks.toLocaleString()}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">分割器:</span>
                    <span class="detail-value">${task.splitter.toUpperCase()}</span>
                </div>
                <div class="detail-row">
                    <span class="detail-label">强制重建:</span>
                    <span class="detail-value">${task.forceReindex ? '是' : '否'}</span>
                </div>
            </div>
            
            ${errorHtml}
        </div>
    `;
}

// 获取状态文本
function getStatusText(status) {
    const statusMap = {
        'indexing': '索引中',
        'paused': '已暂停',
        'completed': '已完成',
        'error': '错误'
    };
    return statusMap[status] || status;
}

// 暂停任务
async function pauseTask(taskId) {
    const task = currentTasks.find(t => t.taskId === taskId);
    if (!task) return;
    
    showConfirmationModal(
        '暂停索引任务',
        `确定要暂停任务 "${task.path}" 吗？`,
        () => executeTaskAction('pause', taskId)
    );
}

// 继续任务
async function resumeTask(taskId) {
    const task = currentTasks.find(t => t.taskId === taskId);
    if (!task) return;
    
    showConfirmationModal(
        '继续索引任务',
        `确定要继续任务 "${task.path}" 吗？`,
        () => executeTaskAction('resume', taskId)
    );
}

// 执行任务操作
async function executeTaskAction(action, taskId) {
    try {
        const response = await fetch(`${API_BASE}/tasks/${taskId}/${action}`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            }
        });
        
        const data = await response.json();
        
        if (!response.ok || !data.success) {
            throw new Error(data.error || `${action} 操作失败`);
        }
        
        // 显示成功消息
        showSuccess(data.message);
        
        // 立即刷新任务列表
        loadTasks();
        
    } catch (error) {
        console.error(`${action} 任务失败:`, error);
        showError(`${action === 'pause' ? '暂停' : '继续'}任务失败: ${error.message}`);
    } finally {
        hideModal();
    }
}

// 显示确认对话框
function showConfirmationModal(title, message, onConfirm) {
    const modal = document.getElementById('confirmation-modal');
    const modalTitle = document.getElementById('modal-title');
    const modalMessage = document.getElementById('modal-message');
    const confirmBtn = document.getElementById('confirm-btn');
    
    // 检查必要的DOM元素是否存在
    if (!modal || !modalTitle || !modalMessage || !confirmBtn) {
        console.error('模态对话框元素未找到:', {
            modal: !!modal,
            modalTitle: !!modalTitle,
            modalMessage: !!modalMessage,
            confirmBtn: !!confirmBtn
        });
        return;
    }
    
    modalTitle.textContent = title;
    modalMessage.textContent = message;
    
    // 移除之前的事件监听器
    const newConfirmBtn = confirmBtn.cloneNode(true);
    confirmBtn.parentNode.replaceChild(newConfirmBtn, confirmBtn);
    
    // 添加新的事件监听器
    newConfirmBtn.addEventListener('click', onConfirm);
    
    // 显示模态对话框
    modal.classList.add('show');
}

// 隐藏模态对话框
function hideModal() {
    const modal = document.getElementById('confirmation-modal');
    
    if (modal) {
        modal.classList.remove('show');
    }
}

// 显示成功消息
function showSuccess(message) {
    console.log('✅ 成功:', message);
    // 可以在这里添加成功提示的UI组件
    showNotification(message, 'success');
}

// 显示错误消息
function showError(message) {
    console.error('❌ 错误:', message);
    // 可以在这里添加错误提示的UI组件
    showNotification(message, 'error');
}

// 显示通知
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `notification notification-${type}`;
    notification.innerHTML = `
        <div class="notification-content">
            <span class="notification-icon">${type === 'success' ? '✅' : type === 'error' ? '❌' : 'ℹ️'}</span>
            <span class="notification-message">${escapeHtml(message)}</span>
        </div>
        <button class="notification-close" onclick="this.parentElement.remove()">&times;</button>
    `;
    
    // 添加样式
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#d4edda' : type === 'error' ? '#f8d7da' : '#d1ecf1'};
        color: ${type === 'success' ? '#155724' : type === 'error' ? '#721c24' : '#0c5460'};
        border: 1px solid ${type === 'success' ? '#c3e6cb' : type === 'error' ? '#f5c6cb' : '#bee5eb'};
        border-radius: 8px;
        padding: 12px 16px;
        max-width: 400px;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        z-index: 1001;
        animation: slideInRight 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: space-between;
        gap: 12px;
    `;
    
    // 添加到页面
    document.body.appendChild(notification);
    
    // 自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 5000);
}

// HTML转义函数
function escapeHtml(text) {
    const div = document.createElement('div');
    div.textContent = text;
    return div.innerHTML;
}

// 页面可见性变化处理
document.addEventListener('visibilitychange', function() {
    if (document.hidden) {
        stopAutoRefresh();
    } else {
        startAutoRefresh();
        loadTasks(); // 页面重新可见时立即刷新
    }
});

// 键盘快捷键
document.addEventListener('keydown', function(event) {
    // ESC 键关闭模态对话框
    if (event.key === 'Escape') {
        hideModal();
    }
    
    // F5 或 Ctrl+R 刷新任务
    if (event.key === 'F5' || (event.ctrlKey && event.key === 'r')) {
        event.preventDefault();
        loadTasks();
    }
});

// 添加CSS动画
const style = document.createElement('style');
style.textContent = `
    @keyframes slideInRight {
        from {
            opacity: 0;
            transform: translateX(100%);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    .notification-content {
        display: flex;
        align-items: center;
        gap: 8px;
    }
    
    .notification-close {
        background: none;
        border: none;
        font-size: 1.2rem;
        cursor: pointer;
        opacity: 0.7;
        transition: opacity 0.2s;
    }
    
    .notification-close:hover {
        opacity: 1;
    }
    
    .task-details {
        margin-top: 12px;
        padding-top: 12px;
        border-top: 1px solid #e9ecef;
    }
    
    .detail-row {
        display: flex;
        justify-content: space-between;
        margin-bottom: 4px;
        font-size: 0.9rem;
    }
    
    .detail-label {
        color: #6c757d;
        font-weight: 500;
    }
    
    .detail-value {
        color: #495057;
        font-weight: 600;
    }
    
    .error-message {
        margin-top: 12px;
        padding: 8px 12px;
        background: #fff3cd;
        border: 1px solid #ffeaa7;
        border-radius: 6px;
        font-size: 0.85rem;
        color: #856404;
    }
`;
document.head.appendChild(style);

console.log('📱 前端脚本加载完成');