{"permissions": {"allow": ["mcp__zen__chat", "mcp__code-context__search_code", "Bash(grep:*)", "Bash(pnpm install:*)", "<PERSON><PERSON>(mkdir:*)", "Bash(pnpm build:*)", "Ba<PERSON>(docker stats:*)", "<PERSON><PERSON>(docker exec:*)", "<PERSON><PERSON>(docker inspect:*)", "mcp__code-context__clear_index", "mcp__code-context__index_codebase", "mcp__code-context__get_indexing_status", "Bash(find:*)"], "deny": []}}